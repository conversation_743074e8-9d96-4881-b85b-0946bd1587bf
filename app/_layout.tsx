import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect, useState } from 'react';
import 'react-native-reanimated';

import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import { LoadingScreen } from '@/components/onboarding/LoadingScreen';
import { useColorScheme } from '@/hooks/useColorScheme';
import { isOnboardingCompleted } from '@/utils/onboardingStorage';
import { registerServiceWorker } from '@/utils/serviceWorker';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [onboardingComplete, setOnboardingComplete] = useState<boolean | null>(null);

  // Check onboarding status and register service worker
  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        const completed = await isOnboardingCompleted();
        setOnboardingComplete(completed);

        // Navigate to onboarding if not completed
        if (!completed) {
          router.replace('/onboarding/welcome' as any);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding on error
        setOnboardingComplete(false);
        router.replace('/onboarding/welcome' as any);
      }
    };

    checkOnboarding();

    // Register service worker for offline functionality
    registerServiceWorker({
      onSuccess: () => console.log('Service Worker registered successfully'),
      onUpdate: () => console.log('Service Worker update available'),
      onError: (error) => console.error('Service Worker registration failed:', error),
    });
  }, []);

  if (!loaded || onboardingComplete === null) {
    // Show loading while fonts load and onboarding status is checked
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="onboarding" options={{ headerShown: false }} />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </ErrorBoundary>
  );
}
