/**
 * Test page for onboarding functionality
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import {
    getOnboardingState,
    resetOnboarding,
    setOnboardingCompleted,
} from '@/utils/onboardingStorage';
import { Stack, router } from 'expo-router';
import React from 'react';
import { Alert, ScrollView, StyleSheet, View } from 'react-native';

export default function OnboardingTestScreen() {
  const [onboardingState, setOnboardingStateLocal] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const tintColor = useThemeColor({}, 'tint');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const mutedColor = useThemeColor({}, 'muted');

  const loadOnboardingState = async () => {
    try {
      setIsLoading(true);
      const state = await getOnboardingState();
      setOnboardingStateLocal(state);
    } catch (error) {
      console.error('Error loading onboarding state:', error);
      Alert.alert('Error', 'Failed to load onboarding state');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetOnboarding = async () => {
    try {
      setIsLoading(true);
      await resetOnboarding();
      await loadOnboardingState();
      Alert.alert('Success', 'Onboarding has been reset. Restart the app to see the onboarding flow.');
    } catch (error) {
      console.error('Error resetting onboarding:', error);
      Alert.alert('Error', 'Failed to reset onboarding');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompleteOnboarding = async () => {
    try {
      setIsLoading(true);
      await setOnboardingCompleted(true);
      await loadOnboardingState();
      Alert.alert('Success', 'Onboarding marked as completed');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      Alert.alert('Error', 'Failed to complete onboarding');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOnboarding = () => {
    router.push('/onboarding/welcome' as any);
  };

  React.useEffect(() => {
    loadOnboardingState();
  }, []);

  return (
    <ThemedView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Onboarding Test',
          headerShown: true,
        }}
      />

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <IconSymbol name="gear" size={48} color={tintColor} />
          <ThemedText type="title" style={styles.title}>
            Onboarding Test
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Test and manage the onboarding flow
          </ThemedText>
        </View>

        {/* Current State */}
        <View style={[styles.section, { backgroundColor: cardBackground, borderColor }]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Current State
          </ThemedText>

          {onboardingState ? (
            <View style={styles.stateContainer}>
              <View style={styles.stateItem}>
                <ThemedText style={styles.stateLabel}>Completed:</ThemedText>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: onboardingState.completed ? '#10B981' : '#EF4444' }
                ]}>
                  <ThemedText style={styles.statusText}>
                    {onboardingState.completed ? 'Yes' : 'No'}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.stateItem}>
                <ThemedText style={styles.stateLabel}>Current Step:</ThemedText>
                <ThemedText style={styles.stateValue}>
                  {onboardingState.currentStep}
                </ThemedText>
              </View>

              <View style={styles.stateItem}>
                <ThemedText style={styles.stateLabel}>Location Permission:</ThemedText>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: onboardingState.permissionsGranted.location ? '#10B981' : '#EF4444' }
                ]}>
                  <ThemedText style={styles.statusText}>
                    {onboardingState.permissionsGranted.location ? 'Granted' : 'Not Granted'}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.stateItem}>
                <ThemedText style={styles.stateLabel}>Notification Permission:</ThemedText>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: onboardingState.permissionsGranted.notifications ? '#10B981' : '#EF4444' }
                ]}>
                  <ThemedText style={styles.statusText}>
                    {onboardingState.permissionsGranted.notifications ? 'Granted' : 'Not Granted'}
                  </ThemedText>
                </View>
              </View>
            </View>
          ) : (
            <ThemedText style={[styles.loadingText, { color: mutedColor }]}>
              {isLoading ? 'Loading...' : 'No data available'}
            </ThemedText>
          )}
        </View>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Refresh State"
            variant="outline"
            size="medium"
            loading={isLoading}
            onPress={loadOnboardingState}
            leftIcon={<IconSymbol name="arrow.clockwise" size={16} color={tintColor} />}
            style={styles.actionButton}
          />

          <Button
            title="Start Onboarding"
            variant="primary"
            size="medium"
            onPress={handleStartOnboarding}
            leftIcon={<IconSymbol name="play.fill" size={16} color="white" />}
            style={styles.actionButton}
          />

          <Button
            title="Complete Onboarding"
            variant="secondary"
            size="medium"
            loading={isLoading}
            onPress={handleCompleteOnboarding}
            leftIcon={<IconSymbol name="checkmark" size={16} color={tintColor} />}
            style={styles.actionButton}
          />

          <Button
            title="Reset Onboarding"
            variant="outline"
            size="medium"
            loading={isLoading}
            onPress={handleResetOnboarding}
            leftIcon={<IconSymbol name="trash" size={16} color={tintColor} />}
            style={styles.actionButton}
          />
        </View>

        {/* Instructions */}
        <View style={[styles.instructions, { backgroundColor: `${tintColor}10`, borderColor: `${tintColor}30` }]}>
          <IconSymbol name="info.circle" size={20} color={tintColor} />
          <View style={styles.instructionsContent}>
            <ThemedText type="defaultSemiBold" style={styles.instructionsTitle}>
              Instructions
            </ThemedText>
            <ThemedText style={[styles.instructionsText, { color: mutedColor }]}>
              • Use &quot;Reset Onboarding&quot; to clear all data and test the flow{'\n'}
              • &quot;Start Onboarding&quot; will navigate to the welcome screen{'\n'}
              • Restart the app after resetting to see the full flow{'\n'}
              • Check permissions in device settings if needed
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    gap: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    marginBottom: 16,
  },
  stateContainer: {
    gap: 12,
  },
  stateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stateLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  stateValue: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  loadingText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  actions: {
    gap: 12,
  },
  actionButton: {
    width: '100%',
  },
  instructions: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  instructionsContent: {
    flex: 1,
  },
  instructionsTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
