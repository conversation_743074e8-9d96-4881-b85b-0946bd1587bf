/**
 * Onboarding layout with stack navigation
 */

import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';

export default function OnboardingLayout() {
  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          gestureEnabled: false, // Disable swipe back gesture
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen name="welcome" />
        <Stack.Screen name="tutorial-browse" />
        <Stack.Screen name="tutorial-order" />
        <Stack.Screen name="tutorial-favorites" />
        <Stack.Screen name="permissions" />
      </Stack>
      <StatusBar style="auto" />
    </>
  );
}
