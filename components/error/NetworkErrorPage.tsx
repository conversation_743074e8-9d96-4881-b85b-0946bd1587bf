/**
 * Network Error page with connectivity detection and retry mechanisms
 */

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useRetry } from '@/hooks/useRetry';
import { useThemeColor } from '@/hooks/useThemeColor';
import { logNetworkError, logUserAction } from '@/utils/errorLogger';
import { NetworkStatus } from '@/utils/networkUtils';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';

export interface NetworkErrorPageProps {
  title?: string;
  message?: string;
  retryAction?: () => Promise<void>;
  showOfflineFeatures?: boolean;
  customActions?: React.ReactNode;
}

export function NetworkErrorPage({
  title,
  message,
  retryAction,
  showOfflineFeatures = true,
  customActions,
}: NetworkErrorPageProps) {
  const [retryCount, setRetryCount] = useState(0);
  const [nextRetryIn, setNextRetryIn] = useState(0);

  const { networkInfo, isOnline, isOffline, isSlow, refreshStatus } = useNetworkStatus();
  const { state, retry, reset } = useRetry();

  const iconColor = useThemeColor({}, isOffline ? 'error' : 'warning');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const successBackground = useThemeColor({}, 'successBackground');
  const warningBackground = useThemeColor({}, 'warningBackground');

  // Dynamic title and message based on network status
  const dynamicTitle = title || getNetworkTitle(networkInfo.status);
  const dynamicMessage = message || getNetworkMessage(networkInfo.status);

  const handleAutoRetry = useCallback(async () => {
    if (!retryAction) return;

    logUserAction('network_error_auto_retry', {
      attempt: retryCount + 1,
      networkStatus: networkInfo.status,
    });

    try {
      const result = await retry(retryAction, {
        maxAttempts: 1,
      });

      if (result.success) {
        logUserAction('network_error_auto_retry_success');
        router.replace('/');
      } else {
        setRetryCount(prev => prev + 1);
        logNetworkError('Auto retry failed', { attempt: retryCount + 1 });
      }
    } catch (error) {
      setRetryCount(prev => prev + 1);
      logNetworkError('Auto retry error', { error: (error as Error).message });
    }
  }, [retryAction, retryCount, networkInfo.status, retry]);

  // Auto-retry when network comes back online
  useEffect(() => {
    if (isOnline && retryAction && retryCount > 0) {
      handleAutoRetry();
    }
  }, [isOnline, retryAction, retryCount, handleAutoRetry]);

  // Periodic retry for slow connections
  useEffect(() => {
    if (isSlow && retryAction && retryCount < 5) {
      const retryDelay = Math.min(10000 * Math.pow(1.5, retryCount), 60000); // Exponential backoff, max 60s

      const timer = setTimeout(() => {
        handleAutoRetry();
      }, retryDelay);

      // Countdown timer
      setNextRetryIn(retryDelay / 1000);
      const countdownTimer = setInterval(() => {
        setNextRetryIn(prev => {
          if (prev <= 1) {
            clearInterval(countdownTimer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearTimeout(timer);
        clearInterval(countdownTimer);
      };
    }
  }, [isSlow, retryCount, retryAction, handleAutoRetry]);

  const handleManualRetry = async () => {
    logUserAction('network_error_manual_retry', {
      networkStatus: networkInfo.status,
    });

    // Refresh network status first
    await refreshStatus();

    if (!retryAction) {
      // Default retry: reload the page
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
      return;
    }

    reset();

    try {
      const result = await retry(retryAction, {
        maxAttempts: 3,
        baseDelay: 2000,
      });

      if (result.success) {
        logUserAction('network_error_manual_retry_success');
        router.replace('/');
      }
    } catch (error) {
      logNetworkError('Manual retry error', { error: (error as Error).message });
    }
  };

  const handleRefreshStatus = async () => {
    logUserAction('network_error_refresh_status');
    await refreshStatus();
  };

  const offlineFeatures = [
    { title: 'View Saved Orders', href: '/orders/saved', icon: 'bookmark' },
    { title: 'Browse Cached Menu', href: '/menu/cached', icon: 'list.bullet' },
    { title: 'Favorites', href: '/favorites', icon: 'heart' },
    { title: 'Settings', href: '/settings', icon: 'gear' },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Network Status Icon */}
        <View style={styles.iconContainer}>
          <IconSymbol
            name={getNetworkIcon(networkInfo.status) as any}
            size={80}
            color={iconColor}
          />
        </View>

        {/* Network Status */}
        <View style={[
          styles.statusContainer,
          { backgroundColor: getStatusBackgroundColor(networkInfo.status, { successBackground, warningBackground, cardBackground }) }
        ]}>
          <ThemedText type="defaultSemiBold" style={styles.statusText}>
            {getStatusText(networkInfo.status)}
          </ThemedText>

          {networkInfo.effectiveType && (
            <ThemedText style={styles.connectionInfo}>
              Connection: {networkInfo.effectiveType.toUpperCase()}
            </ThemedText>
          )}

          {networkInfo.rtt && (
            <ThemedText style={styles.connectionInfo}>
              Latency: {networkInfo.rtt}ms
            </ThemedText>
          )}
        </View>

        {/* Error Message */}
        <View style={styles.messageContainer}>
          <ThemedText type="title" style={styles.title}>
            {dynamicTitle}
          </ThemedText>

          <ThemedText style={styles.message}>
            {dynamicMessage}
          </ThemedText>
        </View>

        {/* Retry Section */}
        <View style={[styles.retryContainer, { backgroundColor: cardBackground }]}>
          {state.isRetrying ? (
            <LoadingSpinner
              message={`Retrying... (Attempt ${state.attempts})`}
              size="small"
            />
          ) : nextRetryIn > 0 ? (
            <ThemedText style={styles.retryInfo}>
              Next automatic retry in {Math.ceil(nextRetryIn)} seconds...
            </ThemedText>
          ) : null}

          <View style={styles.retryActions}>
            <Button
              title={state.isRetrying ? "Retrying..." : "Try Again"}
              onPress={handleManualRetry}
              loading={state.isRetrying}
              style={styles.retryButton}
            />

            <Button
              title="Check Connection"
              variant="outline"
              onPress={handleRefreshStatus}
              style={styles.retryButton}
            />
          </View>
        </View>

        {/* Offline Features */}
        {showOfflineFeatures && isOffline && (
          <View style={[styles.offlineContainer, { backgroundColor: cardBackground }]}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Available Offline
            </ThemedText>

            <ThemedText style={styles.offlineDescription}>
              You can still access some features while offline:
            </ThemedText>

            <View style={styles.featureGrid}>
              {offlineFeatures.map((feature, index) => (
                <Button
                  key={index}
                  title={feature.title}
                  variant="ghost"
                  onPress={() => {
                    logUserAction('network_error_offline_feature', { feature: feature.title });
                    router.push(feature.href as any);
                  }}
                  style={styles.featureButton}
                  leftIcon={
                    <IconSymbol
                      name={feature.icon as any}
                      size={20}
                      color={iconColor}
                    />
                  }
                />
              ))}
            </View>
          </View>
        )}

        {/* Custom Actions */}
        {customActions && (
          <View style={styles.customActionsContainer}>
            {customActions}
          </View>
        )}

        {/* Help Text */}
        <View style={styles.helpContainer}>
          <ThemedText style={styles.helpText}>
            Having persistent connection issues?{' '}
            <ThemedText type="link" onPress={() => router.push('/help/connectivity' as any)}>
              Get Help
            </ThemedText>
          </ThemedText>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

// Helper functions
function getNetworkTitle(status: NetworkStatus): string {
  switch (status) {
    case NetworkStatus.OFFLINE:
      return "No Internet Connection";
    case NetworkStatus.SLOW:
      return "Slow Connection";
    case NetworkStatus.ONLINE:
      return "Connection Restored";
    default:
      return "Connection Issue";
  }
}

function getNetworkMessage(status: NetworkStatus): string {
  switch (status) {
    case NetworkStatus.OFFLINE:
      return "Please check your internet connection and try again. You can still access some features offline.";
    case NetworkStatus.SLOW:
      return "Your connection seems slow. This might affect the app's performance. We'll keep trying to load your content.";
    case NetworkStatus.ONLINE:
      return "Your connection has been restored. You can now access all features.";
    default:
      return "We're having trouble connecting to our servers. Please check your connection and try again.";
  }
}

function getNetworkIcon(status: NetworkStatus): string {
  switch (status) {
    case NetworkStatus.OFFLINE:
      return "wifi.slash";
    case NetworkStatus.SLOW:
      return "wifi.exclamationmark";
    case NetworkStatus.ONLINE:
      return "wifi";
    default:
      return "network";
  }
}

function getStatusText(status: NetworkStatus): string {
  switch (status) {
    case NetworkStatus.OFFLINE:
      return "Offline";
    case NetworkStatus.SLOW:
      return "Slow Connection";
    case NetworkStatus.ONLINE:
      return "Online";
    default:
      return "Unknown";
  }
}

function getStatusBackgroundColor(status: NetworkStatus, colors: any): string {
  switch (status) {
    case NetworkStatus.OFFLINE:
      return colors.cardBackground;
    case NetworkStatus.SLOW:
      return colors.warningBackground;
    case NetworkStatus.ONLINE:
      return colors.successBackground;
    default:
      return colors.cardBackground;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: '100%',
  },
  iconContainer: {
    marginBottom: 24,
  },
  statusContainer: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: 'center',
    minWidth: 200,
  },
  statusText: {
    fontSize: 16,
    marginBottom: 4,
  },
  connectionInfo: {
    fontSize: 12,
    opacity: 0.8,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 32,
    maxWidth: 400,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    textAlign: 'center',
    lineHeight: 24,
  },
  retryContainer: {
    width: '100%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: 'center',
  },
  retryInfo: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 16,
  },
  retryActions: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  retryButton: {
    flex: 1,
  },
  offlineContainer: {
    width: '100%',
    maxWidth: 400,
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  sectionTitle: {
    textAlign: 'center',
    marginBottom: 12,
  },
  offlineDescription: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 16,
    opacity: 0.8,
  },
  featureGrid: {
    gap: 8,
  },
  featureButton: {
    justifyContent: 'flex-start',
  },
  customActionsContainer: {
    width: '100%',
    maxWidth: 400,
    marginBottom: 24,
  },
  helpContainer: {
    marginTop: 'auto',
    paddingTop: 24,
  },
  helpText: {
    textAlign: 'center',
    fontSize: 14,
  },
});
