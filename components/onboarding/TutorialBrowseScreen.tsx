/**
 * Tutorial screen for browsing restaurants and food items
 */

import React from 'react';
import { View, StyleSheet, Dimensions, ScrollView } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

const { width: screenWidth } = Dimensions.get('window');

export function TutorialBrowseScreen() {
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');

  // Animation values
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-30);
  const searchOpacity = useSharedValue(0);
  const searchScale = useSharedValue(0.9);
  const categoriesOpacity = useSharedValue(0);
  const categoriesTranslateX = useSharedValue(-50);
  const restaurantsOpacity = useSharedValue(0);
  const restaurantsTranslateY = useSharedValue(50);

  // Start animations on mount
  React.useEffect(() => {
    // Header animation
    headerOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    headerTranslateY.value = withDelay(200, withSpring(0, { damping: 15, stiffness: 150 }));

    // Search animation
    searchOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    searchScale.value = withDelay(500, withSpring(1, { damping: 15, stiffness: 150 }));

    // Categories animation
    categoriesOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    categoriesTranslateX.value = withDelay(800, withSpring(0, { damping: 15, stiffness: 150 }));

    // Restaurants animation
    restaurantsOpacity.value = withDelay(1100, withTiming(1, { duration: 600 }));
    restaurantsTranslateY.value = withDelay(1100, withSpring(0, { damping: 15, stiffness: 150 }));
  }, []);

  // Animated styles
  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const searchStyle = useAnimatedStyle(() => ({
    opacity: searchOpacity.value,
    transform: [{ scale: searchScale.value }],
  }));

  const categoriesStyle = useAnimatedStyle(() => ({
    opacity: categoriesOpacity.value,
    transform: [{ translateX: categoriesTranslateX.value }],
  }));

  const restaurantsStyle = useAnimatedStyle(() => ({
    opacity: restaurantsOpacity.value,
    transform: [{ translateY: restaurantsTranslateY.value }],
  }));

  const categories = [
    { name: 'Pizza', icon: 'circle', color: '#FF6B6B' },
    { name: 'Burger', icon: 'circle.fill', color: '#4ECDC4' },
    { name: 'Sushi', icon: 'circle', color: '#45B7D1' },
    { name: 'Dessert', icon: 'circle.fill', color: '#FFA07A' },
  ];

  const restaurants = [
    { name: 'Mario\'s Pizza', rating: 4.8, time: '25-35 min', image: '🍕' },
    { name: 'Burger Palace', rating: 4.6, time: '20-30 min', image: '🍔' },
    { name: 'Sushi Express', rating: 4.9, time: '30-40 min', image: '🍣' },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Animated.View style={[styles.header, headerStyle]}>
          <ThemedText type="title" style={styles.title}>
            Browse & Discover
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Find restaurants and food items near you with our smart search and filtering options
          </ThemedText>
        </Animated.View>

        {/* Search Demo */}
        <Animated.View style={[styles.section, searchStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Smart Search
          </ThemedText>
          <View style={[styles.searchDemo, { backgroundColor: cardBackground, borderColor }]}>
            <IconSymbol name="magnifyingglass" size={20} color={mutedColor} />
            <ThemedText style={[styles.searchText, { color: mutedColor }]}>
              Search for "pizza near me"
            </ThemedText>
            <View style={[styles.searchButton, { backgroundColor: tintColor }]}>
              <IconSymbol name="arrow.right" size={16} color="white" />
            </View>
          </View>
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <IconSymbol name="checkmark.circle.fill" size={20} color={tintColor} />
              <ThemedText style={styles.featureText}>Location-based results</ThemedText>
            </View>
            <View style={styles.featureItem}>
              <IconSymbol name="checkmark.circle.fill" size={20} color={tintColor} />
              <ThemedText style={styles.featureText}>Filter by cuisine, price, rating</ThemedText>
            </View>
          </View>
        </Animated.View>

        {/* Categories Demo */}
        <Animated.View style={[styles.section, categoriesStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Browse Categories
          </ThemedText>
          <View style={styles.categoriesGrid}>
            {categories.map((category, index) => (
              <View 
                key={category.name} 
                style={[
                  styles.categoryCard, 
                  { backgroundColor: cardBackground, borderColor }
                ]}
              >
                <View style={[styles.categoryIcon, { backgroundColor: `${category.color}20` }]}>
                  <IconSymbol 
                    name={category.icon as any} 
                    size={24} 
                    color={category.color} 
                  />
                </View>
                <ThemedText style={styles.categoryName}>{category.name}</ThemedText>
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Restaurants Demo */}
        <Animated.View style={[styles.section, restaurantsStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Restaurant Listings
          </ThemedText>
          {restaurants.map((restaurant, index) => (
            <View 
              key={restaurant.name} 
              style={[
                styles.restaurantCard, 
                { backgroundColor: cardBackground, borderColor }
              ]}
            >
              <View style={styles.restaurantImage}>
                <ThemedText style={styles.restaurantEmoji}>{restaurant.image}</ThemedText>
              </View>
              <View style={styles.restaurantInfo}>
                <ThemedText type="defaultSemiBold" style={styles.restaurantName}>
                  {restaurant.name}
                </ThemedText>
                <View style={styles.restaurantMeta}>
                  <View style={styles.rating}>
                    <IconSymbol name="star.fill" size={14} color="#FFD700" />
                    <ThemedText style={styles.ratingText}>{restaurant.rating}</ThemedText>
                  </View>
                  <ThemedText style={[styles.deliveryTime, { color: mutedColor }]}>
                    {restaurant.time}
                  </ThemedText>
                </View>
              </View>
              <IconSymbol name="chevron.right" size={16} color={mutedColor} />
            </View>
          ))}
        </Animated.View>

        {/* Tips */}
        <View style={[styles.tipsSection, { backgroundColor: `${tintColor}10`, borderColor: `${tintColor}30` }]}>
          <IconSymbol name="lightbulb" size={24} color={tintColor} />
          <View style={styles.tipsContent}>
            <ThemedText type="defaultSemiBold" style={styles.tipsTitle}>
              Pro Tips
            </ThemedText>
            <ThemedText style={[styles.tipsText, { color: mutedColor }]}>
              • Use filters to find exactly what you're craving{'\n'}
              • Check ratings and reviews before ordering{'\n'}
              • Save your favorites for quick access
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  header: {
    marginBottom: 32,
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
    fontSize: 18,
  },
  searchDemo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    gap: 12,
  },
  searchText: {
    flex: 1,
    fontSize: 16,
  },
  searchButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  featureList: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  categoryCard: {
    width: (screenWidth - 60) / 2,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    gap: 8,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
  },
  restaurantCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
    gap: 12,
  },
  restaurantImage: {
    width: 48,
    height: 48,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
  },
  restaurantEmoji: {
    fontSize: 24,
  },
  restaurantInfo: {
    flex: 1,
  },
  restaurantName: {
    fontSize: 16,
    marginBottom: 4,
  },
  restaurantMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
  },
  deliveryTime: {
    fontSize: 14,
  },
  tipsSection: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
    marginTop: 16,
  },
  tipsContent: {
    flex: 1,
  },
  tipsTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
