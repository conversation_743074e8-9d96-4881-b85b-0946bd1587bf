/**
 * Tutorial screen for managing favorites and account settings
 */

import React from 'react';
import { View, StyleSheet, Dimensions, ScrollView } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  withTiming,
  withSequence,
} from 'react-native-reanimated';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

const { width: screenWidth } = Dimensions.get('window');

export function TutorialFavoritesScreen() {
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const errorColor = useThemeColor({}, 'error');

  // Animation values
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-30);
  const favoritesOpacity = useSharedValue(0);
  const favoritesScale = useSharedValue(0.9);
  const accountOpacity = useSharedValue(0);
  const accountTranslateX = useSharedValue(-50);
  const heartScale = useSharedValue(1);

  // Start animations on mount
  React.useEffect(() => {
    // Header animation
    headerOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    headerTranslateY.value = withDelay(200, withSpring(0, { damping: 15, stiffness: 150 }));

    // Favorites animation
    favoritesOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    favoritesScale.value = withDelay(500, withSpring(1, { damping: 15, stiffness: 150 }));

    // Account animation
    accountOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    accountTranslateX.value = withDelay(800, withSpring(0, { damping: 15, stiffness: 150 }));

    // Heart animation
    heartScale.value = withDelay(1200, withSequence(
      withSpring(1.3, { damping: 10, stiffness: 200 }),
      withSpring(1, { damping: 15, stiffness: 150 })
    ));
  }, []);

  // Animated styles
  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const favoritesStyle = useAnimatedStyle(() => ({
    opacity: favoritesOpacity.value,
    transform: [{ scale: favoritesScale.value }],
  }));

  const accountStyle = useAnimatedStyle(() => ({
    opacity: accountOpacity.value,
    transform: [{ translateX: accountTranslateX.value }],
  }));

  const heartStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
  }));

  const favoriteItems = [
    { name: 'Mario\'s Pizza', type: 'Restaurant', image: '🍕', saved: true },
    { name: 'Margherita Pizza', type: 'Food Item', image: '🍕', saved: true },
    { name: 'Sushi Express', type: 'Restaurant', image: '🍣', saved: true },
    { name: 'Chocolate Cake', type: 'Food Item', image: '🍰', saved: false },
  ];

  const accountFeatures = [
    { icon: 'person.circle', title: 'Profile', description: 'Manage personal information' },
    { icon: 'location', title: 'Addresses', description: 'Save delivery locations' },
    { icon: 'creditcard', title: 'Payment', description: 'Manage payment methods' },
    { icon: 'bell', title: 'Notifications', description: 'Control notification settings' },
    { icon: 'clock', title: 'Order History', description: 'View past orders' },
    { icon: 'gear', title: 'Preferences', description: 'App settings and preferences' },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Animated.View style={[styles.header, headerStyle]}>
          <ThemedText type="title" style={styles.title}>
            Favorites & Account
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Save your favorites and manage your account settings for a personalized experience
          </ThemedText>
        </Animated.View>

        {/* Favorites Section */}
        <Animated.View style={[styles.section, favoritesStyle]}>
          <View style={styles.sectionHeader}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Your Favorites
            </ThemedText>
            <Animated.View style={heartStyle}>
              <IconSymbol name="heart.fill" size={24} color={errorColor} />
            </Animated.View>
          </View>
          
          <View style={styles.favoritesGrid}>
            {favoriteItems.map((item, index) => (
              <View 
                key={`${item.name}-${index}`}
                style={[
                  styles.favoriteCard, 
                  { backgroundColor: cardBackground, borderColor }
                ]}
              >
                <View style={styles.favoriteHeader}>
                  <View style={styles.favoriteImage}>
                    <ThemedText style={styles.favoriteEmoji}>{item.image}</ThemedText>
                  </View>
                  <View style={[
                    styles.favoriteHeart,
                    { backgroundColor: item.saved ? errorColor : borderColor }
                  ]}>
                    <IconSymbol 
                      name={item.saved ? "heart.fill" : "heart"} 
                      size={12} 
                      color="white" 
                    />
                  </View>
                </View>
                <ThemedText type="defaultSemiBold" style={styles.favoriteName}>
                  {item.name}
                </ThemedText>
                <ThemedText style={[styles.favoriteType, { color: mutedColor }]}>
                  {item.type}
                </ThemedText>
              </View>
            ))}
          </View>

          <View style={[styles.favoriteTips, { backgroundColor: `${errorColor}10`, borderColor: `${errorColor}30` }]}>
            <IconSymbol name="heart" size={20} color={errorColor} />
            <View style={styles.favoriteTipsContent}>
              <ThemedText type="defaultSemiBold" style={styles.favoriteTipsTitle}>
                Quick Access
              </ThemedText>
              <ThemedText style={[styles.favoriteTipsText, { color: mutedColor }]}>
                Tap the heart icon to save restaurants and dishes for quick reordering
              </ThemedText>
            </View>
          </View>
        </Animated.View>

        {/* Account Section */}
        <Animated.View style={[styles.section, accountStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Account Management
          </ThemedText>
          
          <View style={styles.accountGrid}>
            {accountFeatures.map((feature, index) => (
              <View 
                key={feature.title}
                style={[
                  styles.accountCard, 
                  { backgroundColor: cardBackground, borderColor }
                ]}
              >
                <View style={[styles.accountIcon, { backgroundColor: `${tintColor}15` }]}>
                  <IconSymbol 
                    name={feature.icon as any} 
                    size={20} 
                    color={tintColor} 
                  />
                </View>
                <View style={styles.accountContent}>
                  <ThemedText type="defaultSemiBold" style={styles.accountTitle}>
                    {feature.title}
                  </ThemedText>
                  <ThemedText style={[styles.accountDescription, { color: mutedColor }]}>
                    {feature.description}
                  </ThemedText>
                </View>
                <IconSymbol name="chevron.right" size={16} color={mutedColor} />
              </View>
            ))}
          </View>
        </Animated.View>

        {/* Profile Demo */}
        <View style={[styles.profileDemo, { backgroundColor: cardBackground, borderColor }]}>
          <View style={styles.profileHeader}>
            <View style={[styles.profileAvatar, { backgroundColor: tintColor }]}>
              <IconSymbol name="person.fill" size={24} color="white" />
            </View>
            <View style={styles.profileInfo}>
              <ThemedText type="defaultSemiBold" style={styles.profileName}>
                John Doe
              </ThemedText>
              <ThemedText style={[styles.profileEmail, { color: mutedColor }]}>
                <EMAIL>
              </ThemedText>
            </View>
            <IconSymbol name="pencil" size={16} color={tintColor} />
          </View>
          
          <View style={styles.profileStats}>
            <View style={styles.profileStat}>
              <ThemedText type="defaultSemiBold" style={styles.profileStatNumber}>
                24
              </ThemedText>
              <ThemedText style={[styles.profileStatLabel, { color: mutedColor }]}>
                Orders
              </ThemedText>
            </View>
            <View style={[styles.profileStatDivider, { backgroundColor: borderColor }]} />
            <View style={styles.profileStat}>
              <ThemedText type="defaultSemiBold" style={styles.profileStatNumber}>
                8
              </ThemedText>
              <ThemedText style={[styles.profileStatLabel, { color: mutedColor }]}>
                Favorites
              </ThemedText>
            </View>
            <View style={[styles.profileStatDivider, { backgroundColor: borderColor }]} />
            <View style={styles.profileStat}>
              <ThemedText type="defaultSemiBold" style={styles.profileStatNumber}>
                4.9
              </ThemedText>
              <ThemedText style={[styles.profileStatLabel, { color: mutedColor }]}>
                Rating
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Benefits */}
        <View style={[styles.benefitsSection, { backgroundColor: `${tintColor}10`, borderColor: `${tintColor}30` }]}>
          <IconSymbol name="star.fill" size={24} color={tintColor} />
          <View style={styles.benefitsContent}>
            <ThemedText type="defaultSemiBold" style={styles.benefitsTitle}>
              Account Benefits
            </ThemedText>
            <ThemedText style={[styles.benefitsText, { color: mutedColor }]}>
              • Faster checkout with saved information{'\n'}
              • Personalized recommendations{'\n'}
              • Order history and easy reordering{'\n'}
              • Exclusive offers and rewards
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  header: {
    marginBottom: 32,
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
  },
  favoritesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  favoriteCard: {
    width: (screenWidth - 60) / 2,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  favoriteHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  favoriteImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f0f0f0',
  },
  favoriteEmoji: {
    fontSize: 20,
  },
  favoriteHeart: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoriteName: {
    fontSize: 14,
    marginBottom: 2,
  },
  favoriteType: {
    fontSize: 12,
  },
  favoriteTips: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  favoriteTipsContent: {
    flex: 1,
  },
  favoriteTipsTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  favoriteTipsText: {
    fontSize: 12,
    lineHeight: 16,
  },
  accountGrid: {
    gap: 8,
  },
  accountCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    gap: 12,
  },
  accountIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  accountContent: {
    flex: 1,
  },
  accountTitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  accountDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  profileDemo: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  profileAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 16,
    marginBottom: 2,
  },
  profileEmail: {
    fontSize: 14,
  },
  profileStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileStat: {
    flex: 1,
    alignItems: 'center',
  },
  profileStatNumber: {
    fontSize: 18,
    marginBottom: 2,
  },
  profileStatLabel: {
    fontSize: 12,
  },
  profileStatDivider: {
    width: 1,
    height: 32,
  },
  benefitsSection: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  benefitsContent: {
    flex: 1,
  },
  benefitsTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  benefitsText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
