/**
 * Tutorial screen for placing orders and tracking deliveries
 */

import React from 'react';
import { View, StyleSheet, Dimensions, ScrollView } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  withTiming,
  withRepeat,
} from 'react-native-reanimated';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

const { width: screenWidth } = Dimensions.get('window');

export function TutorialOrderScreen() {
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const borderColor = useThemeColor({}, 'border');
  const successColor = useThemeColor({}, 'success');

  // Animation values
  const headerOpacity = useSharedValue(0);
  const headerTranslateY = useSharedValue(-30);
  const orderOpacity = useSharedValue(0);
  const orderScale = useSharedValue(0.9);
  const trackingOpacity = useSharedValue(0);
  const trackingTranslateX = useSharedValue(50);
  const pulseScale = useSharedValue(1);

  // Start animations on mount
  React.useEffect(() => {
    // Header animation
    headerOpacity.value = withDelay(200, withTiming(1, { duration: 600 }));
    headerTranslateY.value = withDelay(200, withSpring(0, { damping: 15, stiffness: 150 }));

    // Order animation
    orderOpacity.value = withDelay(500, withTiming(1, { duration: 600 }));
    orderScale.value = withDelay(500, withSpring(1, { damping: 15, stiffness: 150 }));

    // Tracking animation
    trackingOpacity.value = withDelay(800, withTiming(1, { duration: 600 }));
    trackingTranslateX.value = withDelay(800, withSpring(0, { damping: 15, stiffness: 150 }));

    // Pulse animation for tracking indicator
    pulseScale.value = withRepeat(
      withTiming(1.1, { duration: 1000 }),
      -1,
      true
    );
  }, []);

  // Animated styles
  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: headerTranslateY.value }],
  }));

  const orderStyle = useAnimatedStyle(() => ({
    opacity: orderOpacity.value,
    transform: [{ scale: orderScale.value }],
  }));

  const trackingStyle = useAnimatedStyle(() => ({
    opacity: trackingOpacity.value,
    transform: [{ translateX: trackingTranslateX.value }],
  }));

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const orderSteps = [
    { step: 1, title: 'Select Items', description: 'Browse menu and add to cart', icon: 'plus.circle' },
    { step: 2, title: 'Customize', description: 'Add special instructions', icon: 'pencil' },
    { step: 3, title: 'Checkout', description: 'Choose payment & delivery', icon: 'creditcard' },
    { step: 4, title: 'Confirm', description: 'Review and place order', icon: 'checkmark.circle' },
  ];

  const trackingSteps = [
    { status: 'confirmed', title: 'Order Confirmed', time: '2:30 PM', active: false },
    { status: 'preparing', title: 'Preparing Food', time: '2:45 PM', active: true },
    { status: 'ready', title: 'Ready for Pickup', time: '3:15 PM', active: false },
    { status: 'delivering', title: 'Out for Delivery', time: '3:20 PM', active: false },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Animated.View style={[styles.header, headerStyle]}>
          <ThemedText type="title" style={styles.title}>
            Order & Track
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Place orders easily and track your deliveries in real-time
          </ThemedText>
        </Animated.View>

        {/* Order Process */}
        <Animated.View style={[styles.section, orderStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Easy Ordering Process
          </ThemedText>
          
          <View style={styles.orderSteps}>
            {orderSteps.map((step, index) => (
              <View key={step.step} style={styles.orderStep}>
                <View style={[styles.stepNumber, { backgroundColor: tintColor }]}>
                  <ThemedText style={styles.stepNumberText}>{step.step}</ThemedText>
                </View>
                <View style={styles.stepContent}>
                  <View style={styles.stepHeader}>
                    <IconSymbol name={step.icon as any} size={20} color={tintColor} />
                    <ThemedText type="defaultSemiBold" style={styles.stepTitle}>
                      {step.title}
                    </ThemedText>
                  </View>
                  <ThemedText style={[styles.stepDescription, { color: mutedColor }]}>
                    {step.description}
                  </ThemedText>
                </View>
                {index < orderSteps.length - 1 && (
                  <View style={[styles.stepConnector, { backgroundColor: borderColor }]} />
                )}
              </View>
            ))}
          </View>

          {/* Order Summary Demo */}
          <View style={[styles.orderSummary, { backgroundColor: cardBackground, borderColor }]}>
            <ThemedText type="defaultSemiBold" style={styles.orderSummaryTitle}>
              Order Summary
            </ThemedText>
            <View style={styles.orderItem}>
              <ThemedText style={styles.orderItemName}>Margherita Pizza (Large)</ThemedText>
              <ThemedText style={styles.orderItemPrice}>$18.99</ThemedText>
            </View>
            <View style={styles.orderItem}>
              <ThemedText style={styles.orderItemName}>Garlic Bread</ThemedText>
              <ThemedText style={styles.orderItemPrice}>$6.99</ThemedText>
            </View>
            <View style={[styles.orderTotal, { borderTopColor: borderColor }]}>
              <ThemedText type="defaultSemiBold" style={styles.orderTotalText}>
                Total: $25.98
              </ThemedText>
            </View>
          </View>
        </Animated.View>

        {/* Tracking Demo */}
        <Animated.View style={[styles.section, trackingStyle]}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Real-time Tracking
          </ThemedText>
          
          <View style={[styles.trackingCard, { backgroundColor: cardBackground, borderColor }]}>
            <View style={styles.trackingHeader}>
              <View style={styles.trackingInfo}>
                <ThemedText type="defaultSemiBold" style={styles.trackingTitle}>
                  Order #12345
                </ThemedText>
                <ThemedText style={[styles.trackingSubtitle, { color: mutedColor }]}>
                  Mario's Pizza • Estimated: 25-35 min
                </ThemedText>
              </View>
              <Animated.View style={[styles.trackingIndicator, { backgroundColor: successColor }, pulseStyle]}>
                <IconSymbol name="location.fill" size={16} color="white" />
              </Animated.View>
            </View>

            <View style={styles.trackingSteps}>
              {trackingSteps.map((step, index) => (
                <View key={step.status} style={styles.trackingStep}>
                  <View style={[
                    styles.trackingStepIcon,
                    { 
                      backgroundColor: step.active ? successColor : borderColor,
                    }
                  ]}>
                    {step.active ? (
                      <IconSymbol name="checkmark" size={12} color="white" />
                    ) : (
                      <View style={[styles.trackingStepDot, { 
                        backgroundColor: step.status === 'confirmed' ? successColor : 'transparent' 
                      }]} />
                    )}
                  </View>
                  <View style={styles.trackingStepContent}>
                    <ThemedText 
                      type="defaultSemiBold" 
                      style={[
                        styles.trackingStepTitle,
                        { color: step.active ? successColor : undefined }
                      ]}
                    >
                      {step.title}
                    </ThemedText>
                    <ThemedText style={[styles.trackingStepTime, { color: mutedColor }]}>
                      {step.time}
                    </ThemedText>
                  </View>
                  {index < trackingSteps.length - 1 && (
                    <View style={[styles.trackingConnector, { backgroundColor: borderColor }]} />
                  )}
                </View>
              ))}
            </View>
          </View>
        </Animated.View>

        {/* Features */}
        <View style={[styles.featuresSection, { backgroundColor: `${tintColor}10`, borderColor: `${tintColor}30` }]}>
          <IconSymbol name="star.fill" size={24} color={tintColor} />
          <View style={styles.featuresContent}>
            <ThemedText type="defaultSemiBold" style={styles.featuresTitle}>
              Order Features
            </ThemedText>
            <ThemedText style={[styles.featuresText, { color: mutedColor }]}>
              • Multiple payment options (card, digital wallet){'\n'}
              • Schedule orders for later{'\n'}
              • Real-time GPS tracking{'\n'}
              • Order history and reordering
            </ThemedText>
          </View>
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  header: {
    marginBottom: 32,
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    marginBottom: 16,
    fontSize: 18,
  },
  orderSteps: {
    marginBottom: 24,
  },
  orderStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    position: 'relative',
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  stepContent: {
    flex: 1,
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  stepTitle: {
    fontSize: 16,
  },
  stepDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  stepConnector: {
    position: 'absolute',
    left: 15,
    top: 32,
    width: 2,
    height: 24,
  },
  orderSummary: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  orderSummaryTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  orderItemName: {
    flex: 1,
    fontSize: 14,
  },
  orderItemPrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  orderTotal: {
    borderTopWidth: 1,
    paddingTop: 12,
    marginTop: 8,
  },
  orderTotalText: {
    fontSize: 16,
    textAlign: 'right',
  },
  trackingCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  trackingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  trackingInfo: {
    flex: 1,
  },
  trackingTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  trackingSubtitle: {
    fontSize: 14,
  },
  trackingIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trackingSteps: {
    gap: 16,
  },
  trackingStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    position: 'relative',
  },
  trackingStepIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  trackingStepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  trackingStepContent: {
    flex: 1,
  },
  trackingStepTitle: {
    fontSize: 14,
    marginBottom: 2,
  },
  trackingStepTime: {
    fontSize: 12,
  },
  trackingConnector: {
    position: 'absolute',
    left: 11,
    top: 24,
    width: 2,
    height: 16,
  },
  featuresSection: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
    marginTop: 16,
  },
  featuresContent: {
    flex: 1,
  },
  featuresTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  featuresText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
