/**
 * Welcome screen component for onboarding flow
 */

import React from 'react';
import { View, StyleSheet, Dimensions, Platform } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withDelay,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { Image } from 'expo-image';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface WelcomeScreenProps {
  onGetStarted?: () => void;
}

export function WelcomeScreen({ onGetStarted }: WelcomeScreenProps) {
  const tintColor = useThemeColor({}, 'tint');
  const mutedColor = useThemeColor({}, 'muted');
  const backgroundColor = useThemeColor({}, 'background');

  // Animation values
  const logoScale = useSharedValue(0);
  const logoOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(50);
  const titleOpacity = useSharedValue(0);
  const subtitleTranslateY = useSharedValue(50);
  const subtitleOpacity = useSharedValue(0);
  const featuresOpacity = useSharedValue(0);
  const featuresTranslateY = useSharedValue(30);

  // Start animations on mount
  React.useEffect(() => {
    // Logo animation
    logoScale.value = withDelay(300, withSpring(1, { damping: 15, stiffness: 150 }));
    logoOpacity.value = withDelay(300, withTiming(1, { duration: 600 }));

    // Title animation
    titleTranslateY.value = withDelay(600, withSpring(0, { damping: 15, stiffness: 150 }));
    titleOpacity.value = withDelay(600, withTiming(1, { duration: 600 }));

    // Subtitle animation
    subtitleTranslateY.value = withDelay(900, withSpring(0, { damping: 15, stiffness: 150 }));
    subtitleOpacity.value = withDelay(900, withTiming(1, { duration: 600 }));

    // Features animation
    featuresTranslateY.value = withDelay(1200, withSpring(0, { damping: 15, stiffness: 150 }));
    featuresOpacity.value = withDelay(1200, withTiming(1, { duration: 600 }));
  }, []);

  // Animated styles
  const logoStyle = useAnimatedStyle(() => ({
    transform: [{ scale: logoScale.value }],
    opacity: logoOpacity.value,
  }));

  const titleStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: titleTranslateY.value }],
    opacity: titleOpacity.value,
  }));

  const subtitleStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: subtitleTranslateY.value }],
    opacity: subtitleOpacity.value,
  }));

  const featuresStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: featuresTranslateY.value }],
    opacity: featuresOpacity.value,
  }));

  const features = [
    {
      icon: 'magnifyingglass',
      title: 'Discover',
      description: 'Find restaurants and cuisines you love',
    },
    {
      icon: 'cart',
      title: 'Order',
      description: 'Quick and easy ordering process',
    },
    {
      icon: 'location',
      title: 'Track',
      description: 'Real-time delivery tracking',
    },
    {
      icon: 'heart',
      title: 'Save',
      description: 'Keep your favorites for quick access',
    },
  ];

  return (
    <ThemedView style={styles.container}>
      {/* Logo Section */}
      <View style={styles.logoSection}>
        <Animated.View style={[styles.logoContainer, logoStyle]}>
          {/* App Logo */}
          <View style={[styles.logoBackground, { backgroundColor: tintColor }]}>
            <IconSymbol 
              name="fork.knife" 
              size={60} 
              color="white" 
            />
          </View>
        </Animated.View>

        {/* Title */}
        <Animated.View style={titleStyle}>
          <ThemedText type="title" style={styles.title}>
            Welcome to FoodApp
          </ThemedText>
        </Animated.View>

        {/* Subtitle */}
        <Animated.View style={subtitleStyle}>
          <ThemedText style={[styles.subtitle, { color: mutedColor }]}>
            Your favorite food delivery app{'\n'}
            Fast, fresh, and delivered to your door
          </ThemedText>
        </Animated.View>
      </View>

      {/* Features Section */}
      <Animated.View style={[styles.featuresSection, featuresStyle]}>
        <ThemedText type="subtitle" style={styles.featuresTitle}>
          What you'll love:
        </ThemedText>
        
        <View style={styles.featuresGrid}>
          {features.map((feature, index) => (
            <View key={feature.title} style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: `${tintColor}15` }]}>
                <IconSymbol 
                  name={feature.icon as any} 
                  size={24} 
                  color={tintColor} 
                />
              </View>
              <ThemedText type="defaultSemiBold" style={styles.featureTitle}>
                {feature.title}
              </ThemedText>
              <ThemedText style={[styles.featureDescription, { color: mutedColor }]}>
                {feature.description}
              </ThemedText>
            </View>
          ))}
        </View>
      </Animated.View>

      {/* Bottom decoration */}
      <View style={styles.bottomDecoration}>
        <View style={[styles.decorationDot, { backgroundColor: `${tintColor}30` }]} />
        <View style={[styles.decorationDot, { backgroundColor: `${tintColor}50` }]} />
        <View style={[styles.decorationDot, { backgroundColor: tintColor }]} />
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 20,
  },
  logoSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    marginBottom: 32,
  },
  logoBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
    fontSize: Platform.select({ web: 36, default: 32 }),
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 18,
    lineHeight: 26,
    maxWidth: 300,
  },
  featuresSection: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  featuresTitle: {
    textAlign: 'center',
    marginBottom: 24,
    fontSize: 20,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  featureItem: {
    width: (screenWidth - 60) / 2,
    alignItems: 'center',
    padding: 16,
  },
  featureIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  featureTitle: {
    fontSize: 16,
    marginBottom: 4,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  bottomDecoration: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    paddingBottom: 20,
  },
  decorationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
