/**
 * Custom hook for managing onboarding state and navigation
 */

import { useState, useEffect, useCallback } from 'react';
import { router } from 'expo-router';
import {
  getOnboardingState,
  setOnboardingCompleted,
  setCurrentOnboardingStep,
  setSkipTutorial,
  isOnboardingCompleted,
  type OnboardingState,
} from '@/utils/onboardingStorage';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: string;
  skippable: boolean;
}

export const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to FoodApp',
    description: 'Your favorite food delivery app',
    component: 'welcome',
    skippable: false,
  },
  {
    id: 'browse',
    title: 'Browse & Discover',
    description: 'Find restaurants and food items near you',
    component: 'tutorial-browse',
    skippable: true,
  },
  {
    id: 'order',
    title: 'Order & Track',
    description: 'Place orders and track your deliveries',
    component: 'tutorial-order',
    skippable: true,
  },
  {
    id: 'favorites',
    title: 'Favorites & Account',
    description: 'Manage your favorites and account settings',
    component: 'tutorial-favorites',
    skippable: true,
  },
  {
    id: 'permissions',
    title: 'Enable Features',
    description: 'Allow location and notifications for the best experience',
    component: 'permissions',
    skippable: false,
  },
];

export interface UseOnboardingReturn {
  state: OnboardingState;
  currentStep: OnboardingStep;
  isFirstStep: boolean;
  isLastStep: boolean;
  isLoading: boolean;
  error: string | null;
  progress: number;
  nextStep: () => Promise<void>;
  previousStep: () => Promise<void>;
  skipStep: () => Promise<void>;
  skipTutorial: () => Promise<void>;
  completeOnboarding: () => Promise<void>;
  goToStep: (stepIndex: number) => Promise<void>;
  resetOnboarding: () => Promise<void>;
}

/**
 * Hook for managing onboarding flow
 */
export function useOnboarding(): UseOnboardingReturn {
  const [state, setState] = useState<OnboardingState>({
    completed: false,
    currentStep: 0,
    permissionsGranted: { location: false, notifications: false },
    skipTutorial: false,
    userPreferences: { theme: 'auto', language: 'en', notifications: true },
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const currentStep = ONBOARDING_STEPS[state.currentStep] || ONBOARDING_STEPS[0];
  const isFirstStep = state.currentStep === 0;
  const isLastStep = state.currentStep === ONBOARDING_STEPS.length - 1;
  const progress = ((state.currentStep + 1) / ONBOARDING_STEPS.length) * 100;

  /**
   * Load onboarding state from storage
   */
  const loadState = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const onboardingState = await getOnboardingState();
      setState(onboardingState);
    } catch (err) {
      console.error('Error loading onboarding state:', err);
      setError('Failed to load onboarding state');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Navigate to next step
   */
  const nextStep = useCallback(async () => {
    try {
      setError(null);
      const nextStepIndex = Math.min(state.currentStep + 1, ONBOARDING_STEPS.length - 1);
      
      if (nextStepIndex === ONBOARDING_STEPS.length - 1) {
        // Last step - complete onboarding
        await completeOnboarding();
      } else {
        await setCurrentOnboardingStep(nextStepIndex);
        setState(prev => ({ ...prev, currentStep: nextStepIndex }));
      }
    } catch (err) {
      console.error('Error navigating to next step:', err);
      setError('Failed to proceed to next step');
    }
  }, [state.currentStep]);

  /**
   * Navigate to previous step
   */
  const previousStep = useCallback(async () => {
    try {
      setError(null);
      const prevStepIndex = Math.max(state.currentStep - 1, 0);
      await setCurrentOnboardingStep(prevStepIndex);
      setState(prev => ({ ...prev, currentStep: prevStepIndex }));
    } catch (err) {
      console.error('Error navigating to previous step:', err);
      setError('Failed to go back to previous step');
    }
  }, [state.currentStep]);

  /**
   * Skip current step (if skippable)
   */
  const skipStep = useCallback(async () => {
    try {
      setError(null);
      if (currentStep.skippable) {
        await nextStep();
      }
    } catch (err) {
      console.error('Error skipping step:', err);
      setError('Failed to skip step');
    }
  }, [currentStep.skippable, nextStep]);

  /**
   * Skip entire tutorial (go to permissions step)
   */
  const skipTutorial = useCallback(async () => {
    try {
      setError(null);
      await setSkipTutorial(true);
      
      // Go to permissions step (last step before completion)
      const permissionsStepIndex = ONBOARDING_STEPS.findIndex(step => step.id === 'permissions');
      if (permissionsStepIndex !== -1) {
        await setCurrentOnboardingStep(permissionsStepIndex);
        setState(prev => ({ 
          ...prev, 
          currentStep: permissionsStepIndex,
          skipTutorial: true,
        }));
      }
    } catch (err) {
      console.error('Error skipping tutorial:', err);
      setError('Failed to skip tutorial');
    }
  }, []);

  /**
   * Complete onboarding and navigate to main app
   */
  const completeOnboarding = useCallback(async () => {
    try {
      setError(null);
      await setOnboardingCompleted(true);
      setState(prev => ({ ...prev, completed: true }));
      
      // Navigate to main app
      router.replace('/(tabs)');
    } catch (err) {
      console.error('Error completing onboarding:', err);
      setError('Failed to complete onboarding');
    }
  }, []);

  /**
   * Go to specific step
   */
  const goToStep = useCallback(async (stepIndex: number) => {
    try {
      setError(null);
      const validStepIndex = Math.max(0, Math.min(stepIndex, ONBOARDING_STEPS.length - 1));
      await setCurrentOnboardingStep(validStepIndex);
      setState(prev => ({ ...prev, currentStep: validStepIndex }));
    } catch (err) {
      console.error('Error navigating to step:', err);
      setError('Failed to navigate to step');
    }
  }, []);

  /**
   * Reset onboarding (for testing/debugging)
   */
  const resetOnboarding = useCallback(async () => {
    try {
      setError(null);
      const { clearOnboardingData } = await import('@/utils/onboardingStorage');
      await clearOnboardingData();
      await loadState();
    } catch (err) {
      console.error('Error resetting onboarding:', err);
      setError('Failed to reset onboarding');
    }
  }, [loadState]);

  // Load state on mount
  useEffect(() => {
    loadState();
  }, [loadState]);

  return {
    state,
    currentStep,
    isFirstStep,
    isLastStep,
    isLoading,
    error,
    progress,
    nextStep,
    previousStep,
    skipStep,
    skipTutorial,
    completeOnboarding,
    goToStep,
    resetOnboarding,
  };
}
