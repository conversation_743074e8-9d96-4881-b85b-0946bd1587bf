/**
 * Custom hook for managing app permissions (location and notifications)
 * with proper error handling and user-friendly messaging
 */

import { useState, useEffect, useCallback } from 'react';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { setPermissionStatus, getPermissionStatus, type PermissionStatus } from '@/utils/onboardingStorage';

export interface PermissionState {
  location: {
    status: 'undetermined' | 'granted' | 'denied' | 'restricted';
    canAskAgain: boolean;
  };
  notifications: {
    status: 'undetermined' | 'granted' | 'denied' | 'restricted';
    canAskAgain: boolean;
  };
}

export interface UsePermissionsReturn {
  permissions: PermissionState;
  isLoading: boolean;
  error: string | null;
  requestLocationPermission: () => Promise<boolean>;
  requestNotificationPermission: () => Promise<boolean>;
  requestAllPermissions: () => Promise<PermissionStatus>;
  checkPermissions: () => Promise<void>;
  openSettings: () => void;
}

/**
 * Hook for managing app permissions
 */
export function usePermissions(): UsePermissionsReturn {
  const [permissions, setPermissions] = useState<PermissionState>({
    location: { status: 'undetermined', canAskAgain: true },
    notifications: { status: 'undetermined', canAskAgain: true },
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Check current permission status
   */
  const checkPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check location permission
      const locationStatus = await Location.getForegroundPermissionsAsync();
      
      // Check notification permission
      const notificationStatus = await Notifications.getPermissionsAsync();

      setPermissions({
        location: {
          status: locationStatus.status,
          canAskAgain: locationStatus.canAskAgain,
        },
        notifications: {
          status: notificationStatus.status,
          canAskAgain: notificationStatus.canAskAgain,
        },
      });
    } catch (err) {
      console.error('Error checking permissions:', err);
      setError('Failed to check permissions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Request location permission
   */
  const requestLocationPermission = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      setPermissions(prev => ({
        ...prev,
        location: { status, canAskAgain },
      }));

      const granted = status === 'granted';
      
      // Update stored permission status
      const currentPermissions = await getPermissionStatus();
      await setPermissionStatus({
        ...currentPermissions,
        location: granted,
      });

      if (!granted && !canAskAgain) {
        setError('Location permission was denied. You can enable it in Settings.');
      }

      return granted;
    } catch (err) {
      console.error('Error requesting location permission:', err);
      setError('Failed to request location permission. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Request notification permission
   */
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      // Configure notification handler
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        }),
      });

      const { status, canAskAgain } = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowAnnouncements: true,
        },
      });

      setPermissions(prev => ({
        ...prev,
        notifications: { status, canAskAgain },
      }));

      const granted = status === 'granted';
      
      // Update stored permission status
      const currentPermissions = await getPermissionStatus();
      await setPermissionStatus({
        ...currentPermissions,
        notifications: granted,
      });

      if (!granted && !canAskAgain) {
        setError('Notification permission was denied. You can enable it in Settings.');
      }

      return granted;
    } catch (err) {
      console.error('Error requesting notification permission:', err);
      setError('Failed to request notification permission. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Request all permissions at once
   */
  const requestAllPermissions = useCallback(async (): Promise<PermissionStatus> => {
    try {
      setIsLoading(true);
      setError(null);

      const [locationGranted, notificationGranted] = await Promise.all([
        requestLocationPermission(),
        requestNotificationPermission(),
      ]);

      const result = {
        location: locationGranted,
        notifications: notificationGranted,
      };

      await setPermissionStatus(result);
      return result;
    } catch (err) {
      console.error('Error requesting all permissions:', err);
      setError('Failed to request permissions. Please try again.');
      return { location: false, notifications: false };
    } finally {
      setIsLoading(false);
    }
  }, [requestLocationPermission, requestNotificationPermission]);

  /**
   * Open device settings (platform-specific)
   */
  const openSettings = useCallback(() => {
    if (Platform.OS === 'ios') {
      // On iOS, we can't directly open app settings, but we can provide guidance
      setError('Please go to Settings > Privacy & Security > Location Services to enable location access, or Settings > Notifications to enable notifications.');
    } else if (Platform.OS === 'android') {
      // On Android, we could use Linking to open settings, but for now provide guidance
      setError('Please go to Settings > Apps > FoodApp > Permissions to enable location and notification access.');
    } else {
      // Web platform
      setError('Please check your browser settings to allow location and notification access for this website.');
    }
  }, []);

  // Check permissions on mount
  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  return {
    permissions,
    isLoading,
    error,
    requestLocationPermission,
    requestNotificationPermission,
    requestAllPermissions,
    checkPermissions,
    openSettings,
  };
}
