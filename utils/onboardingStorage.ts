/**
 * Onboarding storage utilities for managing onboarding completion status
 * and user preferences using AsyncStorage
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage keys
const ONBOARDING_KEYS = {
  COMPLETED: '@foodapp/onboarding_completed',
  CURRENT_STEP: '@foodapp/onboarding_current_step',
  PERMISSIONS_GRANTED: '@foodapp/permissions_granted',
  SKIP_TUTORIAL: '@foodapp/skip_tutorial',
  USER_PREFERENCES: '@foodapp/user_preferences',
} as const;

export interface OnboardingState {
  completed: boolean;
  currentStep: number;
  permissionsGranted: {
    location: boolean;
    notifications: boolean;
  };
  skipTutorial: boolean;
  userPreferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    notifications: boolean;
  };
}

export interface PermissionStatus {
  location: boolean;
  notifications: boolean;
}

/**
 * Get the current onboarding state
 */
export async function getOnboardingState(): Promise<OnboardingState> {
  try {
    const [
      completed,
      currentStep,
      permissionsGranted,
      skipTutorial,
      userPreferences,
    ] = await Promise.all([
      AsyncStorage.getItem(ONBOARDING_KEYS.COMPLETED),
      AsyncStorage.getItem(ONBOARDING_KEYS.CURRENT_STEP),
      AsyncStorage.getItem(ONBOARDING_KEYS.PERMISSIONS_GRANTED),
      AsyncStorage.getItem(ONBOARDING_KEYS.SKIP_TUTORIAL),
      AsyncStorage.getItem(ONBOARDING_KEYS.USER_PREFERENCES),
    ]);

    return {
      completed: completed === 'true',
      currentStep: currentStep ? parseInt(currentStep, 10) : 0,
      permissionsGranted: permissionsGranted 
        ? JSON.parse(permissionsGranted) 
        : { location: false, notifications: false },
      skipTutorial: skipTutorial === 'true',
      userPreferences: userPreferences 
        ? JSON.parse(userPreferences)
        : { theme: 'auto', language: 'en', notifications: true },
    };
  } catch (error) {
    console.error('Error getting onboarding state:', error);
    // Return default state on error
    return {
      completed: false,
      currentStep: 0,
      permissionsGranted: { location: false, notifications: false },
      skipTutorial: false,
      userPreferences: { theme: 'auto', language: 'en', notifications: true },
    };
  }
}

/**
 * Check if onboarding has been completed
 */
export async function isOnboardingCompleted(): Promise<boolean> {
  try {
    const completed = await AsyncStorage.getItem(ONBOARDING_KEYS.COMPLETED);
    return completed === 'true';
  } catch (error) {
    console.error('Error checking onboarding completion:', error);
    return false;
  }
}

/**
 * Mark onboarding as completed
 */
export async function setOnboardingCompleted(completed: boolean = true): Promise<void> {
  try {
    await AsyncStorage.setItem(ONBOARDING_KEYS.COMPLETED, completed.toString());
  } catch (error) {
    console.error('Error setting onboarding completion:', error);
    throw error;
  }
}

/**
 * Save the current onboarding step
 */
export async function setCurrentOnboardingStep(step: number): Promise<void> {
  try {
    await AsyncStorage.setItem(ONBOARDING_KEYS.CURRENT_STEP, step.toString());
  } catch (error) {
    console.error('Error setting current onboarding step:', error);
    throw error;
  }
}

/**
 * Get the current onboarding step
 */
export async function getCurrentOnboardingStep(): Promise<number> {
  try {
    const step = await AsyncStorage.getItem(ONBOARDING_KEYS.CURRENT_STEP);
    return step ? parseInt(step, 10) : 0;
  } catch (error) {
    console.error('Error getting current onboarding step:', error);
    return 0;
  }
}

/**
 * Save permission status
 */
export async function setPermissionStatus(permissions: PermissionStatus): Promise<void> {
  try {
    await AsyncStorage.setItem(
      ONBOARDING_KEYS.PERMISSIONS_GRANTED,
      JSON.stringify(permissions)
    );
  } catch (error) {
    console.error('Error setting permission status:', error);
    throw error;
  }
}

/**
 * Get permission status
 */
export async function getPermissionStatus(): Promise<PermissionStatus> {
  try {
    const permissions = await AsyncStorage.getItem(ONBOARDING_KEYS.PERMISSIONS_GRANTED);
    return permissions 
      ? JSON.parse(permissions)
      : { location: false, notifications: false };
  } catch (error) {
    console.error('Error getting permission status:', error);
    return { location: false, notifications: false };
  }
}

/**
 * Set skip tutorial preference
 */
export async function setSkipTutorial(skip: boolean): Promise<void> {
  try {
    await AsyncStorage.setItem(ONBOARDING_KEYS.SKIP_TUTORIAL, skip.toString());
  } catch (error) {
    console.error('Error setting skip tutorial preference:', error);
    throw error;
  }
}

/**
 * Save user preferences
 */
export async function setUserPreferences(preferences: OnboardingState['userPreferences']): Promise<void> {
  try {
    await AsyncStorage.setItem(
      ONBOARDING_KEYS.USER_PREFERENCES,
      JSON.stringify(preferences)
    );
  } catch (error) {
    console.error('Error setting user preferences:', error);
    throw error;
  }
}

/**
 * Clear all onboarding data (useful for testing or reset)
 */
export async function clearOnboardingData(): Promise<void> {
  try {
    await AsyncStorage.multiRemove(Object.values(ONBOARDING_KEYS));
  } catch (error) {
    console.error('Error clearing onboarding data:', error);
    throw error;
  }
}

/**
 * Reset onboarding to initial state
 */
export async function resetOnboarding(): Promise<void> {
  try {
    await clearOnboardingData();
    console.log('Onboarding data reset successfully');
  } catch (error) {
    console.error('Error resetting onboarding:', error);
    throw error;
  }
}
